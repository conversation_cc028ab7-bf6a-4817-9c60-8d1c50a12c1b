<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - DreamBig Real Estate</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/auth.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .dashboard-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .dashboard-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .card-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .card-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .card-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .user-info {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .user-info h2 {
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .user-detail {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #666;
        }

        .user-detail i {
            color: #667eea;
            width: 20px;
        }

        .logout-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .logout-button {
            background: #ff4757;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .logout-button:hover {
            background: #ff3742;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 71, 87, 0.3);
        }

        @media (max-width: 768px) {
            .dashboard-header {
                padding: 30px 20px;
            }

            .dashboard-header h1 {
                font-size: 2rem;
            }

            .dashboard-container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'components/navbar.html' %}

    <div class="dashboard-container">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <h1>Welcome to Your Dashboard</h1>
            <p id="welcomeMessage">Manage your properties, investments, and more</p>
        </div>

        <!-- User Information -->
        <div class="user-info">
            <h2>
                <i class="fas fa-user-circle"></i>
                Your Account Information
            </h2>
            <div class="user-details" id="userDetails">
                <div class="user-detail">
                    <i class="fas fa-envelope"></i>
                    <span id="userEmail">Loading...</span>
                </div>
                <div class="user-detail">
                    <i class="fas fa-user"></i>
                    <span id="userName">Loading...</span>
                </div>
                <div class="user-detail">
                    <i class="fas fa-calendar"></i>
                    <span id="userJoined">Loading...</span>
                </div>
                <div class="user-detail">
                    <i class="fas fa-shield-alt"></i>
                    <span id="userStatus">Loading...</span>
                </div>
            </div>
        </div>

        <!-- Dashboard Cards -->
        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="card-title">My Properties</div>
                <div class="card-description">
                    View and manage your property listings, track inquiries, and update property details.
                </div>
                <a href="/properties" class="card-button">View Properties</a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="card-title">My Investments</div>
                <div class="card-description">
                    Track your real estate investments, view returns, and explore new opportunities.
                </div>
                <a href="/investments" class="card-button">View Investments</a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div class="card-title">Services</div>
                <div class="card-description">
                    Access professional services like legal assistance, property management, and more.
                </div>
                <a href="/services" class="card-button">Browse Services</a>
            </div>
        </div>

        <!-- Logout Section -->
        <div class="logout-section">
            <h3>Session Management</h3>
            <p>Click below to securely log out of your account</p>
            <button class="logout-button" id="dashboardLogout">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </button>
        </div>

        <!-- Success/Error Messages -->
        <div class="message-container" id="messageContainer"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', path='/js/firebase-config.js') }}"></script>
    <script src="{{ url_for('static', path='/js/auth.js') }}"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is authenticated
            if (!authManager.isAuthenticated()) {
                window.location.href = '/login';
                return;
            }

            // Load user information
            loadUserInfo();

            // Setup logout button
            const logoutButton = document.getElementById('dashboardLogout');
            if (logoutButton) {
                logoutButton.addEventListener('click', async () => {
                    await authManager.logout();
                });
            }
        });

        function loadUserInfo() {
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            
            // Update welcome message
            const welcomeMessage = document.getElementById('welcomeMessage');
            if (welcomeMessage && user.displayName) {
                welcomeMessage.textContent = `Welcome back, ${user.displayName}!`;
            }

            // Update user details
            const userEmail = document.getElementById('userEmail');
            const userName = document.getElementById('userName');
            const userJoined = document.getElementById('userJoined');
            const userStatus = document.getElementById('userStatus');

            if (userEmail) userEmail.textContent = user.email || 'Not available';
            if (userName) userName.textContent = user.displayName || 'Not available';
            if (userJoined) userJoined.textContent = 'Member since registration';
            if (userStatus) userStatus.textContent = 'Active Account';
        }
    </script>
</body>
</html>
